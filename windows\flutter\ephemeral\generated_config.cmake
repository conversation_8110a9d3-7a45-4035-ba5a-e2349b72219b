# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\C_41_TaNguyenVu_2001225916\\C_41_TaNguyenVu_2001225916" PROJECT_DIR)

set(FLUTTER_VERSION "0.1.0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\flutter"
  "PROJECT_DIR=D:\\C_41_TaNguyenVu_2001225916\\C_41_TaNguyenVu_2001225916"
  "FLUTTER_ROOT=D:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\C_41_TaNguyenVu_2001225916\\C_41_TaNguyenVu_2001225916\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\C_41_TaNguyenVu_2001225916\\C_41_TaNguyenVu_2001225916"
  "FLUTTER_TARGET=D:\\C_41_TaNguyenVu_2001225916\\C_41_TaNguyenVu_2001225916\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\C_41_TaNguyenVu_2001225916\\C_41_TaNguyenVu_2001225916\\.dart_tool\\package_config.json"
)
