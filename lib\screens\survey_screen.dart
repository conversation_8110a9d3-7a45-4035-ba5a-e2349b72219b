import 'package:flutter/material.dart';
import '../controllers/survey_controller.dart';
import '../controllers/auth_controller.dart';
import '../models/user.dart';
import '../models/khao_sat.dart';
import 'login_screen.dart';
import 'statistics_screen.dart';

class SurveyScreen extends StatefulWidget {
  final User user;

  const SurveyScreen({super.key, required this.user});

  @override
  State<SurveyScreen> createState() => _SurveyScreenState();
}

class _SurveyScreenState extends State<SurveyScreen> {
  final SurveyController _surveyController = SurveyController();
  final AuthController _authController = AuthController();

  List<KhaoSat> _parentSurveys = [];
  Map<int, List<KhaoSat>> _childSurveys = {};
  Map<int, bool> _expandedSurveys = {};
  Set<int> _selectedQuestions = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSurveys();
  }

  Future<void> _loadSurveys() async {
    try {
      final parentSurveys = await _surveyController.getParentSurveys();
      Map<int, List<KhaoSat>> childSurveys = {};
      Map<int, bool> expandedSurveys = {};

      print('DEBUG: Parent surveys count: ${parentSurveys.length}');
      for (var parent in parentSurveys) {
        print('DEBUG: Parent survey: ${parent.content} (ID: ${parent.id})');
        final children = await _surveyController.getChildSurveys(parent.id!);
        print('DEBUG: Children count for ${parent.id}: ${children.length}');
        for (var child in children) {
          print(
            'DEBUG: Child: ${child.content} (ID: ${child.id}, ParentID: ${child.parentId})',
          );
        }
        childSurveys[parent.id!] = children;
        expandedSurveys[parent.id!] = false;
      }

      setState(() {
        _parentSurveys = parentSurveys;
        _childSurveys = childSurveys;
        _expandedSurveys = expandedSurveys;
        _isLoading = false;
      });
    } catch (e) {
      print('DEBUG: Error loading surveys: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi tải khảo sát: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _submitSurveys() async {
    if (_selectedQuestions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn ít nhất một câu hỏi!'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final success = await _surveyController.submitSurveys(
        surveyIds: _selectedQuestions.toList(),
        userId: widget.user.id,
      );

      if (success && mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => StatisticsScreen(user: widget.user),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Lỗi gửi khảo sát!'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Lỗi: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _logout() async {
    await _authController.logout();
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Danh sách khảo sát'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'logout') {
                _logout();
              }
            },
            itemBuilder: (BuildContext context) {
              return [
                const PopupMenuItem<String>(
                  value: 'logout',
                  child: Row(
                    children: [
                      Icon(Icons.logout),
                      SizedBox(width: 8),
                      Text('Đăng xuất'),
                    ],
                  ),
                ),
              ];
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Header with user info
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      border: Border(
                        bottom: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Xin chào, ${widget.user.fullName}!',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Vui lòng tham gia khảo sát',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Surveys list
                  Expanded(
                    child:
                        _parentSurveys.isEmpty
                            ? const Center(
                              child: Text(
                                'Không có khảo sát nào',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            )
                            : ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: _parentSurveys.length,
                              itemBuilder: (context, index) {
                                final parentSurvey = _parentSurveys[index];
                                final isExpanded =
                                    _expandedSurveys[parentSurvey.id] ?? false;
                                final childQuestions =
                                    _childSurveys[parentSurvey.id] ?? [];

                                return Card(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  elevation: 2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Column(
                                    children: [
                                      // Parent survey header
                                      InkWell(
                                        onTap: () {
                                          setState(() {
                                            _expandedSurveys[parentSurvey.id!] =
                                                !isExpanded;
                                          });
                                        },
                                        borderRadius: BorderRadius.circular(12),
                                        child: Container(
                                          padding: const EdgeInsets.all(16),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  parentSurvey.content,
                                                  style: const TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.blue,
                                                  ),
                                                ),
                                              ),
                                              Icon(
                                                isExpanded
                                                    ? Icons.keyboard_arrow_up
                                                    : Icons.keyboard_arrow_down,
                                                color: Colors.blue,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),

                                      // Child questions (expandable)
                                      if (isExpanded) ...[
                                        const Divider(height: 1),
                                        Padding(
                                          padding: const EdgeInsets.all(16),
                                          child: Column(
                                            children:
                                                childQuestions.map((question) {
                                                  final isSelected =
                                                      _selectedQuestions
                                                          .contains(
                                                            question.id,
                                                          );

                                                  return Container(
                                                    margin:
                                                        const EdgeInsets.only(
                                                          bottom: 12,
                                                        ),
                                                    child: InkWell(
                                                      onTap: () {
                                                        setState(() {
                                                          if (isSelected) {
                                                            _selectedQuestions
                                                                .remove(
                                                                  question.id,
                                                                );
                                                          } else {
                                                            _selectedQuestions
                                                                .add(
                                                                  question.id!,
                                                                );
                                                          }
                                                        });
                                                      },
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            8,
                                                          ),
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets.all(
                                                              12,
                                                            ),
                                                        decoration: BoxDecoration(
                                                          color:
                                                              isSelected
                                                                  ? Colors.blue
                                                                      .withOpacity(
                                                                        0.1,
                                                                      )
                                                                  : Colors
                                                                      .grey[50],
                                                          border: Border.all(
                                                            color:
                                                                isSelected
                                                                    ? Colors
                                                                        .blue
                                                                    : Colors
                                                                        .grey[300]!,
                                                            width:
                                                                isSelected
                                                                    ? 2
                                                                    : 1,
                                                          ),
                                                          borderRadius:
                                                              BorderRadius.circular(
                                                                8,
                                                              ),
                                                        ),
                                                        child: Row(
                                                          children: [
                                                            Icon(
                                                              isSelected
                                                                  ? Icons
                                                                      .check_box
                                                                  : Icons
                                                                      .check_box_outline_blank,
                                                              color:
                                                                  isSelected
                                                                      ? Colors
                                                                          .blue
                                                                      : Colors
                                                                          .grey[600],
                                                            ),
                                                            const SizedBox(
                                                              width: 12,
                                                            ),
                                                            Expanded(
                                                              child: Text(
                                                                question
                                                                    .content,
                                                                style: TextStyle(
                                                                  color:
                                                                      isSelected
                                                                          ? Colors
                                                                              .blue
                                                                          : Colors
                                                                              .grey[700],
                                                                  fontWeight:
                                                                      isSelected
                                                                          ? FontWeight
                                                                              .w600
                                                                          : FontWeight
                                                                              .normal,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                }).toList(),
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                );
                              },
                            ),
                  ),

                  // Submit button
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed:
                            _selectedQuestions.isNotEmpty
                                ? _submitSurveys
                                : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.pink,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Bình chọn',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
    );
  }
}
