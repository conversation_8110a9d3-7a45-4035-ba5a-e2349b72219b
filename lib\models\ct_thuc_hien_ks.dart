class CTThucHienKS {
  final int? id;
  final int surveyId;
  final String choice;

  CTThucHienKS({
    this.id,
    required this.surveyId,
    required this.choice,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'surveyId': surveyId,
      'choice': choice,
    };
  }

  factory CTThucHienKS.fromMap(Map<String, dynamic> map) {
    return CTThucHienKS(
      id: map['id'],
      surveyId: map['surveyId'] ?? 0,
      choice: map['choice'] ?? '',
    );
  }

  @override
  String toString() {
    return 'CTThucHienKS{id: $id, surveyId: $surveyId, choice: $choice}';
  }
}
