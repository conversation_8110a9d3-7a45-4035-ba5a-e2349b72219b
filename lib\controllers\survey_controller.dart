import '../database/database_helper.dart';
import '../models/khao_sat.dart';
import '../models/thuc_hien_ks.dart';
import '../models/ct_thuc_hien_ks.dart';

class SurveyController {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<KhaoSat>> getAllSurveys() async {
    try {
      return await _databaseHelper.getAllSurveys();
    } catch (e) {
      print('Lỗi lấy danh sách khảo sát: $e');
      return [];
    }
  }

  Future<List<KhaoSat>> getParentSurveys() async {
    try {
      return await _databaseHelper.getParentSurveys();
    } catch (e) {
      print('Lỗi lấy danh sách khảo sát cha: $e');
      return [];
    }
  }

  Future<List<KhaoSat>> getChildSurveys(int parentId) async {
    try {
      return await _databaseHelper.getChildSurveys(parentId);
    } catch (e) {
      print('Lỗi lấy danh sách câu hỏi con: $e');
      return [];
    }
  }

  Future<Map<String, int>> getSurveyStatistics() async {
    try {
      return await _databaseHelper.getSurveyStatistics();
    } catch (e) {
      print('Lỗi lấy thống kê khảo sát: $e');
      return {};
    }
  }

  Future<bool> submitSurveys({
    required List<int> surveyIds,
    required String userId,
  }) async {
    try {
      for (int surveyId in surveyIds) {
        // Lưu thông tin thực hiện khảo sát
        final thucHienKS = ThucHienKS(
          date: DateTime.now().toIso8601String(),
          surveyId: surveyId,
          userId: userId,
        );

        await _databaseHelper.insertThucHienKS(thucHienKS);

        // Lưu chi tiết lựa chọn
        final ctThucHienKS = CTThucHienKS(
          surveyId: surveyId,
          choice: 'Đã chọn',
        );

        await _databaseHelper.insertCTThucHienKS(ctThucHienKS);
      }

      return true;
    } catch (e) {
      print('Lỗi gửi khảo sát: $e');
      return false;
    }
  }
}
