import '../database/database_helper.dart';
import '../models/khao_sat.dart';
import '../models/thuc_hien_ks.dart';
import '../models/ct_thuc_hien_ks.dart';

class SurveyController {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<List<KhaoSat>> getAllSurveys() async {
    try {
      return await _databaseHelper.getAllSurveys();
    } catch (e) {
      print('Lỗi lấy danh sách khảo sát: $e');
      return [];
    }
  }

  Future<bool> submitSurvey({
    required int surveyId,
    required String userId,
    required String choice,
  }) async {
    try {
      // Lưu thông tin thực hiện khảo sát
      final thucHienKS = ThucHienKS(
        date: DateTime.now().toIso8601String(),
        surveyId: surveyId,
        userId: userId,
      );
      
      await _databaseHelper.insertThucHienKS(thucHienKS);

      // Lưu chi tiết lựa chọn
      final ctThucHienKS = CTThucHienKS(
        surveyId: surveyId,
        choice: choice,
      );
      
      await _databaseHelper.insertCTThucHienKS(ctThucHienKS);
      
      return true;
    } catch (e) {
      print('Lỗi gửi khảo sát: $e');
      return false;
    }
  }
}
