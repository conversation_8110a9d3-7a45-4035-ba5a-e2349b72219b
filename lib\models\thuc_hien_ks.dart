class ThucHienKS {
  final int? id;
  final String date;
  final int surveyId;
  final String userId;

  ThucHienKS({
    this.id,
    required this.date,
    required this.surveyId,
    required this.userId,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date,
      'surveyId': surveyId,
      'userId': userId,
    };
  }

  factory ThucHienKS.fromMap(Map<String, dynamic> map) {
    return ThucHienKS(
      id: map['id'],
      date: map['date'] ?? '',
      surveyId: map['surveyId'] ?? 0,
      userId: map['userId'] ?? '',
    );
  }

  @override
  String toString() {
    return 'ThucHienKS{id: $id, date: $date, surveyId: $surveyId, userId: $userId}';
  }
}
