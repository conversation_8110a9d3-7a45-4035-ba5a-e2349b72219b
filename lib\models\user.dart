class User {
  final String id;
  final String password;
  final String fullName;
  final String gender;
  final String? avatar;

  User({
    required this.id,
    required this.password,
    required this.fullName,
    required this.gender,
    this.avatar,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'password': password,
      'fullName': fullName,
      'gender': gender,
      'avatar': avatar,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] ?? '',
      password: map['password'] ?? '',
      fullName: map['fullName'] ?? '',
      gender: map['gender'] ?? '',
      avatar: map['avatar'],
    );
  }

  @override
  String toString() {
    return 'User{id: $id, fullName: $fullName, gender: $gender, avatar: $avatar}';
  }
}
