import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesService {
  static const String _keyRememberLogin = 'remember_login';
  static const String _keyUserId = 'user_id';
  static const String _keyPassword = 'password';

  static Future<void> saveLoginInfo({
    required String userId,
    required String password,
    required bool rememberLogin,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setBool(_keyRememberLogin, rememberLogin);
    
    if (rememberLogin) {
      await prefs.setString(_keyUserId, userId);
      await prefs.setString(_keyPassword, password);
    } else {
      await prefs.remove(_keyUserId);
      await prefs.remove(_keyPassword);
    }
  }

  static Future<Map<String, dynamic>> getLoginInfo() async {
    final prefs = await SharedPreferences.getInstance();
    
    final rememberLogin = prefs.getBool(_keyRememberLogin) ?? false;
    final userId = prefs.getString(_keyUserId) ?? '';
    final password = prefs.getString(_keyPassword) ?? '';

    return {
      'rememberLogin': rememberLogin,
      'userId': userId,
      'password': password,
    };
  }

  static Future<void> clearLoginInfo() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyRememberLogin);
    await prefs.remove(_keyUserId);
    await prefs.remove(_keyPassword);
  }
}
