{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\C_41_TaNguyenVu_2001225916\\C_41_TaNguyenVu_2001225916\\android\\app\\.cxx\\Debug\\3n76c5th\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\C_41_TaNguyenVu_2001225916\\C_41_TaNguyenVu_2001225916\\android\\app\\.cxx\\Debug\\3n76c5th\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}