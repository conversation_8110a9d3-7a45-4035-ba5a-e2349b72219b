# Ứng dụng Quản lý Chi tiêu Cá nhân

Ứng dụng Flutter sử dụng SQLite để quản lý khảo sát và thông tin người dùng theo mô hình MVC.

## Tính năng chính

### 1. <PERSON><PERSON><PERSON> nhập
- <PERSON>hập username và password
- <PERSON><PERSON><PERSON><PERSON> "<PERSON><PERSON> nhớ mật khẩu"
- Hi<PERSON><PERSON> thị/ẩn mật khẩu
- Tự động điền thông tin đăng nhập đã lưu
- Thông báo lỗi bằng SnackBar

### 2. Trang khảo sát
- Hiển thị thông tin người dùng đã đăng nhập
- <PERSON><PERSON> sách các khảo sát
- Lựa chọn đánh giá (R<PERSON>t hà<PERSON> lòng, <PERSON><PERSON><PERSON> lòng, <PERSON><PERSON><PERSON> thường, Không hài lòng)
- G<PERSON>i kết quả khảo sát
- Đăng xuất

## Cấu trúc cơ sở dữ liệu

### Bảng users
- id (TEXT PRIMARY KEY)
- password (TEXT)
- fullName (TEXT)
- gender (TEXT)
- avatar (TEXT)

### Bảng khao_sat
- id (INTEGER PRIMARY KEY)
- content (TEXT)
- parentId (INTEGER)

### Bảng thuc_hien_ks
- id (INTEGER PRIMARY KEY)
- date (TEXT)
- surveyId (INTEGER)
- userId (TEXT)

### Bảng ct_thuc_hien_ks
- id (INTEGER PRIMARY KEY)
- surveyId (INTEGER)
- choice (TEXT)

## Tài khoản mẫu

- **Username**: admin, **Password**: 123456
- **Username**: user1, **Password**: password

## Cách chạy ứng dụng

1. Cài đặt dependencies:
```bash
flutter pub get
```

2. Chạy trên web:
```bash
flutter run -d web-server --web-port=8080
```

3. Mở trình duyệt tại: http://localhost:8080

## Cấu trúc thư mục

```
lib/
├── controllers/          # Controllers (MVC)
│   ├── auth_controller.dart
│   └── survey_controller.dart
├── database/            # Database helper
│   └── database_helper.dart
├── models/              # Data models
│   ├── user.dart
│   ├── khao_sat.dart
│   ├── thuc_hien_ks.dart
│   └── ct_thuc_hien_ks.dart
├── screens/             # UI screens (Views)
│   ├── login_screen.dart
│   └── survey_screen.dart
├── services/            # Services
│   └── shared_preferences_service.dart
└── main.dart           # Entry point
```

## Công nghệ sử dụng

- **Flutter**: Framework UI
- **SQLite**: Cơ sở dữ liệu local
- **SharedPreferences**: Lưu trữ thông tin đăng nhập
- **Material Design**: Thiết kế UI

## Mô hình MVC

- **Model**: Các class trong thư mục `models/`
- **View**: Các screen trong thư mục `screens/`
- **Controller**: Các controller trong thư mục `controllers/`
