import '../database/database_helper.dart';
import '../models/user.dart';
import '../services/shared_preferences_service.dart';

class AuthController {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<User?> login(String userId, String password) async {
    try {
      return await _databaseHelper.getUser(userId, password);
    } catch (e) {
      print('Lỗi đăng nhập: $e');
      return null;
    }
  }

  Future<void> saveLoginInfo({
    required String userId,
    required String password,
    required bool rememberLogin,
  }) async {
    await SharedPreferencesService.saveLoginInfo(
      userId: userId,
      password: password,
      rememberLogin: rememberLogin,
    );
  }

  Future<Map<String, dynamic>> getSavedLoginInfo() async {
    return await SharedPreferencesService.getLoginInfo();
  }

  Future<void> logout() async {
    await SharedPreferencesService.clearLoginInfo();
  }
}
