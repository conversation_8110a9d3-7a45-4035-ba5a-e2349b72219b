class KhaoSat {
  final int? id;
  final String content;
  final int? parentId;

  KhaoSat({
    this.id,
    required this.content,
    this.parentId,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'content': content,
      'parentId': parentId,
    };
  }

  factory KhaoSat.fromMap(Map<String, dynamic> map) {
    return KhaoSat(
      id: map['id'],
      content: map['content'] ?? '',
      parentId: map['parentId'],
    );
  }

  @override
  String toString() {
    return 'KhaoSat{id: $id, content: $content, parentId: $parentId}';
  }
}
