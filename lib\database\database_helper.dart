import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/khao_sat.dart';
import '../models/thuc_hien_ks.dart';
import '../models/ct_thuc_hien_ks.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'survey_app.db');
    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        password TEXT NOT NULL,
        fullName TEXT NOT NULL,
        gender TEXT NOT NULL,
        avatar TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE khao_sat (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        content TEXT NOT NULL,
        parentId INTEGER,
        FOREIGN KEY (parentId) REFERENCES khao_sat (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE thuc_hien_ks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        surveyId INTEGER NOT NULL,
        userId TEXT NOT NULL,
        FOREIGN KEY (surveyId) REFERENCES khao_sat (id),
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE ct_thuc_hien_ks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        surveyId INTEGER NOT NULL,
        choice TEXT NOT NULL,
        FOREIGN KEY (surveyId) REFERENCES khao_sat (id)
      )
    ''');

    await _insertSampleData(db);
  }

  Future<void> _insertSampleData(Database db) async {
    // Thêm user mẫu
    await db.insert('users', {
      'id': 'admin',
      'password': '123456',
      'fullName': 'Quản trị viên',
      'gender': 'Nam',
      'avatar': null,
    });

    await db.insert('users', {
      'id': 'user1',
      'password': 'password',
      'fullName': 'Nguyễn Văn A',
      'gender': 'Nam',
      'avatar': null,
    });

    // Thêm khảo sát chính
    int survey1Id = await db.insert('khao_sat', {
      'content': 'Khảo sát về sức khỏe cá nhân',
      'parentId': null,
    });

    int survey2Id = await db.insert('khao_sat', {
      'content': 'Khảo sát về chế độ dinh dưỡng',
      'parentId': null,
    });

    // Thêm câu hỏi con cho khảo sát 1
    await db.insert('khao_sat', {
      'content': 'Bạn tập thể dục bao nhiêu lần mỗi tuần',
      'parentId': survey1Id,
    });

    await db.insert('khao_sat', {
      'content': 'Bạn có ngủ đủ 8 tiếng mỗi ngày không ?',
      'parentId': survey1Id,
    });

    await db.insert('khao_sat', {
      'content': 'Bạn có kiểm tra sức khỏe định kỳ không ?',
      'parentId': survey1Id,
    });

    // Thêm câu hỏi con cho khảo sát 2
    await db.insert('khao_sat', {
      'content': 'Bạn có ăn đủ rau và hoa quả mỗi ngày không ?',
      'parentId': survey2Id,
    });

    await db.insert('khao_sat', {
      'content': 'Bạn có thường xuyên ăn thức ăn nhanh không?',
      'parentId': survey2Id,
    });

    await db.insert('khao_sat', {
      'content': 'Bạn có uống đủ nước mỗi ngày không ?',
      'parentId': survey2Id,
    });
  }

  Future<User?> getUser(String id, String password) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ? AND password = ?',
      whereArgs: [id, password],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('users', user.toMap());
  }

  Future<List<KhaoSat>> getAllSurveys() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('khao_sat');
    return List.generate(maps.length, (i) => KhaoSat.fromMap(maps[i]));
  }

  Future<List<KhaoSat>> getParentSurveys() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'khao_sat',
      where: 'parentId IS NULL',
    );
    return List.generate(maps.length, (i) => KhaoSat.fromMap(maps[i]));
  }

  Future<List<KhaoSat>> getChildSurveys(int parentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'khao_sat',
      where: 'parentId = ?',
      whereArgs: [parentId],
    );
    return List.generate(maps.length, (i) => KhaoSat.fromMap(maps[i]));
  }

  Future<Map<String, int>> getSurveyStatistics() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT ks.content, COUNT(ct.id) as count
      FROM khao_sat ks
      LEFT JOIN ct_thuc_hien_ks ct ON ks.id = ct.surveyId
      WHERE ks.parentId IS NOT NULL
      GROUP BY ks.id, ks.content
    ''');

    Map<String, int> statistics = {};
    for (var map in maps) {
      statistics[map['content']] = map['count'] ?? 0;
    }
    return statistics;
  }

  Future<int> insertSurvey(KhaoSat survey) async {
    final db = await database;
    return await db.insert('khao_sat', survey.toMap());
  }

  Future<int> insertThucHienKS(ThucHienKS thucHienKS) async {
    final db = await database;
    return await db.insert('thuc_hien_ks', thucHienKS.toMap());
  }

  Future<int> insertCTThucHienKS(CTThucHienKS ctThucHienKS) async {
    final db = await database;
    return await db.insert('ct_thuc_hien_ks', ctThucHienKS.toMap());
  }

  Future<void> resetDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'survey_app.db');

    // Đóng database hiện tại nếu có
    if (_database != null) {
      final db = _database!;
      await db.close();
    }

    // Xóa database cũ
    await deleteDatabase(path);

    // Tạo lại database mới
    _database = null;
    await database; // Gọi getter để tạo database mới
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
