{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\mobile\\flutter\\Buoi11\\bttl\\android\\app\\.cxx\\Debug\\5g6z1k3b\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\mobile\\flutter\\Buoi11\\bttl\\android\\app\\.cxx\\Debug\\5g6z1k3b\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}